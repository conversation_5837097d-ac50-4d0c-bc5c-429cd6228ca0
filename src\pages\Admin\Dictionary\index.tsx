import {
  createRegionDict,
  createRelationshipDict,
  createTypeDict,
  deleteRegionDict,
  deleteRelationshipDict,
  deleteTypeDict,
  getRegionDictList,
  getRegionDictTree,
  getRelationshipDictList,
  getTypeDictList,
  getTypeDictTree,
  RegionDict,
  RelationshipDict,
  toggleRegionDictStatus,
  toggleRelationshipDictStatus,
  toggleTypeDictStatus,
  TypeDict,
  updateRegionDict,
  updateRelationshipDict,
  updateTypeDict,
} from '@/services/dictionary';
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import {
  Badge,
  Button,
  Card,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Switch,
  Table,
  Tabs,
  Tree,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const AdminDictionary: React.FC = () => {
  // 数据状态
  const [regionData, setRegionData] = useState<RegionDict[]>([]);
  const [typeData, setTypeData] = useState<TypeDict[]>([]);
  const [relationData, setRelationData] = useState<RelationshipDict[]>([]);
  const [regionTreeData, setRegionTreeData] = useState<RegionDict[]>([]);
  const [typeTreeData, setTypeTreeData] = useState<TypeDict[]>([]);

  // UI状态
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [currentTab, setCurrentTab] = useState('region');
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [statusFilter, setStatusFilter] = useState<number | undefined>(
    undefined,
  );
  const [viewMode, setViewMode] = useState<'list' | 'tree'>('list');

  // 批量操作状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [batchLoading, setBatchLoading] = useState(false);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const [form] = Form.useForm();

  // 数据加载函数
  const loadRegionData = async (
    page = 1,
    pageSize = 10,
    keyword = '',
    status?: number,
  ) => {
    try {
      setLoading(true);
      const response = await getRegionDictList({
        page,
        pageSize,
        keyword,
        status,
      });
      if (response.errCode === 0 && response.data) {
        setRegionData(response.data.list);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      }
    } catch (error) {
      message.error('加载区域字典失败');
    } finally {
      setLoading(false);
    }
  };

  const loadTypeData = async (
    page = 1,
    pageSize = 10,
    keyword = '',
    status?: number,
  ) => {
    try {
      setLoading(true);
      const response = await getTypeDictList({
        page,
        pageSize,
        keyword,
        status,
      });
      if (response.errCode === 0 && response.data) {
        setTypeData(response.data.list);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      }
    } catch (error) {
      message.error('加载类型字典失败');
    } finally {
      setLoading(false);
    }
  };

  const loadRelationData = async (
    page = 1,
    pageSize = 10,
    keyword = '',
    status?: number,
  ) => {
    try {
      setLoading(true);
      const response = await getRelationshipDictList({
        page,
        pageSize,
        keyword,
        status,
      });
      if (response.errCode === 0 && response.data) {
        setRelationData(response.data.list);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      }
    } catch (error) {
      message.error('加载关系字典失败');
    } finally {
      setLoading(false);
    }
  };

  const loadTreeData = async (type: string) => {
    try {
      setLoading(true);
      if (type === 'region') {
        const response = await getRegionDictTree();
        if (response.errCode === 0 && response.data) {
          setRegionTreeData(response.data);
        }
      } else if (type === 'type') {
        const response = await getTypeDictTree();
        if (response.errCode === 0 && response.data) {
          setTypeTreeData(response.data);
        }
      }
    } catch (error) {
      message.error('加载树形数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    if (currentTab === 'region') {
      if (viewMode === 'list') {
        loadRegionData(
          pagination.current,
          pagination.pageSize,
          searchKeyword,
          statusFilter,
        );
      } else {
        loadTreeData('region');
      }
    } else if (currentTab === 'type') {
      if (viewMode === 'list') {
        loadTypeData(
          pagination.current,
          pagination.pageSize,
          searchKeyword,
          statusFilter,
        );
      } else {
        loadTreeData('type');
      }
    } else if (currentTab === 'relation') {
      loadRelationData(
        pagination.current,
        pagination.pageSize,
        searchKeyword,
        statusFilter,
      );
    }
  }, [
    currentTab,
    viewMode,
    pagination.current,
    pagination.pageSize,
    searchKeyword,
    statusFilter,
  ]);

  const handleAdd = (type: string) => {
    setEditingItem(null);
    setCurrentTab(type);
    form.resetFields();
    form.setFieldsValue({ status: 1, parentId: null, sort: 1 });
    setModalVisible(true);
  };

  const handleEdit = (record: any, type: string) => {
    setEditingItem(record);
    setCurrentTab(type);
    form.setFieldsValue({
      ...record,
      parentId: record.parentId || null,
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: number, type: string) => {
    try {
      let response;
      switch (type) {
        case 'region':
          response = await deleteRegionDict(id);
          break;
        case 'type':
          response = await deleteTypeDict(id);
          break;
        case 'relation':
          response = await deleteRelationshipDict(id);
          break;
        default:
          return;
      }

      if (response.errCode === 0) {
        message.success('删除成功！');
        // 重新加载数据
        if (type === 'region') {
          if (viewMode === 'list') {
            loadRegionData(
              pagination.current,
              pagination.pageSize,
              searchKeyword,
              statusFilter,
            );
          } else {
            loadTreeData('region');
          }
        } else if (type === 'type') {
          if (viewMode === 'list') {
            loadTypeData(
              pagination.current,
              pagination.pageSize,
              searchKeyword,
              statusFilter,
            );
          } else {
            loadTreeData('type');
          }
        } else if (type === 'relation') {
          loadRelationData(
            pagination.current,
            pagination.pageSize,
            searchKeyword,
            statusFilter,
          );
        }
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 处理parentId为null的情况
      if (values.parentId === 0 || values.parentId === '') {
        values.parentId = null;
      }

      let response;
      if (editingItem) {
        // 编辑
        switch (currentTab) {
          case 'region':
            response = await updateRegionDict(editingItem.id, values);
            break;
          case 'type':
            response = await updateTypeDict(editingItem.id, values);
            break;
          case 'relation':
            response = await updateRelationshipDict(editingItem.id, values);
            break;
          default:
            return;
        }

        if (response.errCode === 0) {
          message.success('编辑成功！');
        } else {
          message.error(response.msg || '编辑失败');
          return;
        }
      } else {
        // 新增
        switch (currentTab) {
          case 'region':
            response = await createRegionDict(values);
            break;
          case 'type':
            response = await createTypeDict(values);
            break;
          case 'relation':
            response = await createRelationshipDict(values);
            break;
          default:
            return;
        }

        if (response.errCode === 0) {
          message.success('添加成功！');
        } else {
          message.error(response.msg || '添加失败');
          return;
        }
      }

      setModalVisible(false);

      // 重新加载数据
      if (currentTab === 'region') {
        if (viewMode === 'list') {
          loadRegionData(
            pagination.current,
            pagination.pageSize,
            searchKeyword,
            statusFilter,
          );
        } else {
          loadTreeData('region');
        }
      } else if (currentTab === 'type') {
        if (viewMode === 'list') {
          loadTypeData(
            pagination.current,
            pagination.pageSize,
            searchKeyword,
            statusFilter,
          );
        } else {
          loadTreeData('type');
        }
      } else if (currentTab === 'relation') {
        loadRelationData(
          pagination.current,
          pagination.pageSize,
          searchKeyword,
          statusFilter,
        );
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleStatusToggle = async (id: number, type: string) => {
    try {
      let response;
      switch (type) {
        case 'region':
          response = await toggleRegionDictStatus(id);
          break;
        case 'type':
          response = await toggleTypeDictStatus(id);
          break;
        case 'relation':
          response = await toggleRelationshipDictStatus(id);
          break;
        default:
          return;
      }

      if (response.errCode === 0) {
        message.success(response.data?.message || '状态更新成功');
        // 重新加载数据
        if (type === 'region') {
          if (viewMode === 'list') {
            loadRegionData(
              pagination.current,
              pagination.pageSize,
              searchKeyword,
              statusFilter,
            );
          } else {
            loadTreeData('region');
          }
        } else if (type === 'type') {
          if (viewMode === 'list') {
            loadTypeData(
              pagination.current,
              pagination.pageSize,
              searchKeyword,
              statusFilter,
            );
          } else {
            loadTreeData('type');
          }
        } else if (type === 'relation') {
          loadRelationData(
            pagination.current,
            pagination.pageSize,
            searchKeyword,
            statusFilter,
          );
        }
      } else {
        message.error(response.msg || '状态更新失败');
      }
    } catch (error) {
      message.error('状态更新失败');
    }
  };

  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setPagination({ ...pagination, current: 1 });
  };

  const handleStatusFilterChange = (value: number | undefined) => {
    setStatusFilter(value);
    setPagination({ ...pagination, current: 1 });
  };

  const handleViewModeChange = (mode: 'list' | 'tree') => {
    setViewMode(mode);
  };

  const handleTableChange = (paginationInfo: any) => {
    setPagination({
      current: paginationInfo.current,
      pageSize: paginationInfo.pageSize,
      total: pagination.total,
    });
  };

  // 批量操作函数
  const handleBatchStatusUpdate = async (status: number) => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要操作的记录');
      return;
    }

    try {
      setBatchLoading(true);
      let response;
      const ids = selectedRowKeys.map((key) => Number(key));

      switch (currentTab) {
        case 'region':
          response = await import('@/services/dictionary').then((module) =>
            module.batchUpdateRegionDictStatus({ ids, status }),
          );
          break;
        case 'type':
          response = await import('@/services/dictionary').then((module) =>
            module.batchUpdateTypeDictStatus({ ids, status }),
          );
          break;
        case 'relation':
          response = await import('@/services/dictionary').then((module) =>
            module.batchUpdateRelationshipDictStatus({ ids, status }),
          );
          break;
        default:
          return;
      }

      if (response.errCode === 0) {
        message.success(`批量${status === 1 ? '启用' : '禁用'}成功`);
        setSelectedRowKeys([]);
        // 重新加载数据
        if (currentTab === 'region') {
          loadRegionData(
            pagination.current,
            pagination.pageSize,
            searchKeyword,
            statusFilter,
          );
        } else if (currentTab === 'type') {
          loadTypeData(
            pagination.current,
            pagination.pageSize,
            searchKeyword,
            statusFilter,
          );
        } else if (currentTab === 'relation') {
          loadRelationData(
            pagination.current,
            pagination.pageSize,
            searchKeyword,
            statusFilter,
          );
        }
      } else {
        message.error(response.msg || '批量操作失败');
      }
    } catch (error) {
      message.error('批量操作失败');
    } finally {
      setBatchLoading(false);
    }
  };

  const handleSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: handleSelectChange,
  };

  const regionColumns: TableColumnsType<RegionDict> = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
    {
      title: '区域编码',
      dataIndex: 'regionCode',
      key: 'regionCode',
      width: 120,
    },
    { title: '区域名称', dataIndex: 'regionName', key: 'regionName' },
    { title: '父级ID', dataIndex: 'parentId', key: 'parentId', width: 80 },
    { title: '排序', dataIndex: 'sort', key: 'sort', width: 80 },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: number, record: RegionDict) => (
        <Space>
          <Badge status={status === 1 ? 'success' : 'default'} />
          <Switch
            checked={status === 1}
            onChange={() => handleStatusToggle(record.id, 'region')}
            size="small"
          />
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'regionDesc',
      key: 'regionDesc',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: RegionDict) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record, 'region')}
            style={{ padding: 0 }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id, 'region')}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              style={{ padding: 0 }}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const typeColumns: TableColumnsType<TypeDict> = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
    { title: '类型编码', dataIndex: 'typeCode', key: 'typeCode', width: 120 },
    { title: '类型名称', dataIndex: 'typeName', key: 'typeName' },
    { title: '父级ID', dataIndex: 'parentId', key: 'parentId', width: 80 },
    { title: '排序', dataIndex: 'sort', key: 'sort', width: 80 },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: number, record: TypeDict) => (
        <Space>
          <Badge status={status === 1 ? 'success' : 'default'} />
          <Switch
            checked={status === 1}
            onChange={() => handleStatusToggle(record.id, 'type')}
            size="small"
          />
        </Space>
      ),
    },
    { title: '描述', dataIndex: 'typeDesc', key: 'typeDesc', ellipsis: true },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: TypeDict) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record, 'type')}
            style={{ padding: 0 }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id, 'type')}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              style={{ padding: 0 }}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const relationColumns: TableColumnsType<RelationshipDict> = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
    {
      title: '关系编码',
      dataIndex: 'relationCode',
      key: 'relationCode',
      width: 120,
    },
    { title: '关系名称', dataIndex: 'relationName', key: 'relationName' },
    { title: '父级ID', dataIndex: 'parentId', key: 'parentId', width: 80 },
    { title: '排序', dataIndex: 'sort', key: 'sort', width: 80 },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: number, record: RelationshipDict) => (
        <Space>
          <Badge status={status === 1 ? 'success' : 'default'} />
          <Switch
            checked={status === 1}
            onChange={() => handleStatusToggle(record.id, 'relation')}
            size="small"
          />
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'relationDesc',
      key: 'relationDesc',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: RelationshipDict) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record, 'relation')}
            style={{ padding: 0 }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id, 'relation')}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              style={{ padding: 0 }}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const getModalTitle = () => {
    const action = editingItem ? '编辑' : '添加';
    switch (currentTab) {
      case 'region':
        return `${action}区域字典`;
      case 'type':
        return `${action}类型字典`;
      case 'relation':
        return `${action}关系字典`;
      default:
        return `${action}字典`;
    }
  };

  const renderFormFields = () => {
    switch (currentTab) {
      case 'region':
        return (
          <>
            <Form.Item
              name="regionCode"
              label="区域编码"
              rules={[
                { required: true, message: '请输入区域编码' },
                { max: 50, message: '编码长度不能超过50个字符' },
              ]}
            >
              <Input placeholder="请输入区域编码" />
            </Form.Item>
            <Form.Item
              name="regionName"
              label="区域名称"
              rules={[
                { required: true, message: '请输入区域名称' },
                { max: 255, message: '名称长度不能超过255个字符' },
              ]}
            >
              <Input placeholder="请输入区域名称" />
            </Form.Item>
            <Form.Item name="regionDesc" label="区域描述">
              <TextArea rows={3} placeholder="请输入区域描述" />
            </Form.Item>
          </>
        );
      case 'type':
        return (
          <>
            <Form.Item
              name="typeCode"
              label="类型编码"
              rules={[
                { required: true, message: '请输入类型编码' },
                { max: 50, message: '编码长度不能超过50个字符' },
              ]}
            >
              <Input placeholder="请输入类型编码" />
            </Form.Item>
            <Form.Item
              name="typeName"
              label="类型名称"
              rules={[
                { required: true, message: '请输入类型名称' },
                { max: 255, message: '名称长度不能超过255个字符' },
              ]}
            >
              <Input placeholder="请输入类型名称" />
            </Form.Item>
            <Form.Item name="typeDesc" label="类型描述">
              <TextArea rows={3} placeholder="请输入类型描述" />
            </Form.Item>
          </>
        );
      case 'relation':
        return (
          <>
            <Form.Item
              name="relationCode"
              label="关系编码"
              rules={[
                { required: true, message: '请输入关系编码' },
                { max: 50, message: '编码长度不能超过50个字符' },
              ]}
            >
              <Input placeholder="请输入关系编码" />
            </Form.Item>
            <Form.Item
              name="relationName"
              label="关系名称"
              rules={[
                { required: true, message: '请输入关系名称' },
                { max: 255, message: '名称长度不能超过255个字符' },
              ]}
            >
              <Input placeholder="请输入关系名称" />
            </Form.Item>
            <Form.Item name="relationDesc" label="关系描述">
              <TextArea rows={3} placeholder="请输入关系描述" />
            </Form.Item>
          </>
        );
    }
  };

  // 渲染工具栏
  const renderToolbar = (type: string) => (
    <div style={{ marginBottom: 16 }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 8,
        }}
      >
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAdd(type)}
          >
            添加{type === 'region' ? '区域' : type === 'type' ? '类型' : '关系'}
          </Button>
          {(type === 'region' || type === 'type') && (
            <Button.Group>
              <Button
                type={viewMode === 'list' ? 'primary' : 'default'}
                onClick={() => handleViewModeChange('list')}
              >
                列表视图
              </Button>
              <Button
                type={viewMode === 'tree' ? 'primary' : 'default'}
                onClick={() => handleViewModeChange('tree')}
              >
                树形视图
              </Button>
            </Button.Group>
          )}
        </Space>
        <Space>
          <Input.Search
            placeholder="搜索编码或名称"
            allowClear
            style={{ width: 200 }}
            onSearch={handleSearch}
          />
          <Select
            placeholder="状态筛选"
            allowClear
            style={{ width: 120 }}
            onChange={handleStatusFilterChange}
          >
            <Option value={1}>启用</Option>
            <Option value={0}>禁用</Option>
          </Select>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              if (type === 'region') {
                if (viewMode === 'list') {
                  loadRegionData(1, pagination.pageSize, '', undefined);
                } else {
                  loadTreeData('region');
                }
              } else if (type === 'type') {
                if (viewMode === 'list') {
                  loadTypeData(1, pagination.pageSize, '', undefined);
                } else {
                  loadTreeData('type');
                }
              } else if (type === 'relation') {
                loadRelationData(1, pagination.pageSize, '', undefined);
              }
              setSearchKeyword('');
              setStatusFilter(undefined);
              setSelectedRowKeys([]);
            }}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 批量操作栏 */}
      {viewMode === 'list' && selectedRowKeys.length > 0 && (
        <div
          style={{
            marginBottom: 16,
            padding: '8px 16px',
            background: '#f0f2f5',
            borderRadius: '6px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Space>
            <span>已选择 {selectedRowKeys.length} 项</span>
            <Button size="small" onClick={() => setSelectedRowKeys([])}>
              取消选择
            </Button>
          </Space>
          <Space>
            <Button
              size="small"
              loading={batchLoading}
              onClick={() => handleBatchStatusUpdate(1)}
            >
              批量启用
            </Button>
            <Button
              size="small"
              loading={batchLoading}
              onClick={() => handleBatchStatusUpdate(0)}
            >
              批量禁用
            </Button>
          </Space>
        </div>
      )}
    </div>
  );

  // 渲染树形数据
  const renderTreeData = (data: any[], type: string) => {
    const convertToTreeData = (items: any[]): any[] => {
      return items.map((item) => ({
        title: `${item[type === 'region' ? 'regionName' : 'typeName']} (${
          item[type === 'region' ? 'regionCode' : 'typeCode']
        })`,
        key: item.id,
        children: item.children ? convertToTreeData(item.children) : [],
        data: item,
      }));
    };

    return (
      <Tree
        treeData={convertToTreeData(data)}
        titleRender={(nodeData: any) => (
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
            }}
          >
            <span>{nodeData.title}</span>
            <Space size="small">
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleEdit(nodeData.data, type);
                }}
                style={{ padding: 0 }}
              >
                编辑
              </Button>
              <Popconfirm
                title="确定要删除这条记录吗？"
                onConfirm={(e) => {
                  e?.stopPropagation();
                  handleDelete(nodeData.data.id, type);
                }}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="link"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={(e) => e.stopPropagation()}
                  style={{ padding: 0 }}
                >
                  删除
                </Button>
              </Popconfirm>
            </Space>
          </div>
        )}
      />
    );
  };

  const tabItems = [
    {
      key: 'region',
      label: '区域字典',
      children: (
        <div>
          {renderToolbar('region')}
          {viewMode === 'list' ? (
            <Table
              columns={regionColumns}
              dataSource={regionData}
              rowKey="id"
              loading={loading}
              rowSelection={rowSelection}
              pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
              onChange={handleTableChange}
            />
          ) : (
            <Card>
              {regionTreeData.length > 0 ? (
                renderTreeData(regionTreeData, 'region')
              ) : (
                <div>暂无数据</div>
              )}
            </Card>
          )}
        </div>
      ),
    },
    {
      key: 'type',
      label: '类型字典',
      children: (
        <div>
          {renderToolbar('type')}
          {viewMode === 'list' ? (
            <Table
              columns={typeColumns}
              dataSource={typeData}
              rowKey="id"
              loading={loading}
              rowSelection={rowSelection}
              pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
              onChange={handleTableChange}
            />
          ) : (
            <Card>
              {typeTreeData.length > 0 ? (
                renderTreeData(typeTreeData, 'type')
              ) : (
                <div>暂无数据</div>
              )}
            </Card>
          )}
        </div>
      ),
    },
    {
      key: 'relation',
      label: '关系字典',
      children: (
        <div>
          {renderToolbar('relation')}
          <Table
            columns={relationColumns}
            dataSource={relationData}
            rowKey="id"
            loading={loading}
            rowSelection={rowSelection}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
            onChange={handleTableChange}
          />
        </div>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        字典管理
      </Title>

      <Tabs
        items={tabItems}
        activeKey={currentTab}
        onChange={(key) => {
          setCurrentTab(key);
          setViewMode('list');
          setSearchKeyword('');
          setStatusFilter(undefined);
          setSelectedRowKeys([]);
          setPagination({ current: 1, pageSize: 10, total: 0 });
        }}
        tabBarStyle={{ marginBottom: 16 }}
      />

      <Modal
        title={getModalTitle()}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnClose
        confirmLoading={loading}
      >
        <Form form={form} layout="vertical">
          {renderFormFields()}
          <Form.Item name="parentId" label="父级ID">
            <InputNumber
              style={{ width: '100%' }}
              placeholder="留空表示顶级"
              min={0}
            />
          </Form.Item>
          <Form.Item
            name="sort"
            label="排序"
            rules={[{ required: true, message: '请输入排序号' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入排序号"
              min={1}
            />
          </Form.Item>
          <Form.Item name="status" label="状态" valuePropName="checked">
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminDictionary;
