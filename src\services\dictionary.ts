import { request } from '@umijs/max';

// ==================== 字典管理 API ====================

/**
 * 区域字典相关接口
 */

// 区域字典数据类型
export interface RegionDict {
  id: number;
  regionCode: string;
  regionName: string;
  parentId?: number | null;
  status: number;
  sort: number;
  regionDesc?: string;
  createdAt?: string;
  updatedAt?: string;
  parent?: RegionDict | null;
  children?: RegionDict[];
}

// 区域字典创建参数
export interface CreateRegionDictParams {
  regionCode: string;
  regionName: string;
  parentId?: number | null;
  status?: number;
  sort?: number;
  regionDesc?: string;
}

// 区域字典更新参数
export interface UpdateRegionDictParams {
  regionCode?: string;
  regionName?: string;
  parentId?: number | null;
  status?: number;
  sort?: number;
  regionDesc?: string;
}

// 区域字典列表查询参数
export interface GetRegionDictListParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  status?: number;
  parentId?: number;
}

// 区域字典列表响应
export interface RegionDictListResponse {
  list: RegionDict[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 创建区域字典
 */
export async function createRegionDict(
  params: CreateRegionDictParams,
): Promise<API.ResType<RegionDict>> {
  return request('/admin/region-dict/', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新区域字典
 */
export async function updateRegionDict(
  id: number,
  params: UpdateRegionDictParams,
): Promise<API.ResType<RegionDict>> {
  return request(`/admin/region-dict/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除区域字典
 */
export async function deleteRegionDict(
  id: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/region-dict/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取区域字典详情
 */
export async function getRegionDictDetail(
  id: number,
): Promise<API.ResType<RegionDict>> {
  return request(`/admin/region-dict/${id}`, {
    method: 'GET',
  });
}

/**
 * 获取区域字典列表（分页）
 */
export async function getRegionDictList(
  params?: GetRegionDictListParams,
): Promise<API.ResType<RegionDictListResponse>> {
  return request('/admin/region-dict/', {
    method: 'GET',
    params,
  });
}

/**
 * 获取区域字典树形结构
 */
export async function getRegionDictTree(): Promise<API.ResType<RegionDict[]>> {
  return request('/admin/region-dict/tree', {
    method: 'GET',
  });
}

/**
 * 获取所有区域字典（缓存）
 */
export async function getAllRegionDict(): Promise<API.ResType<RegionDict[]>> {
  return request('/admin/region-dict/all', {
    method: 'GET',
  });
}

/**
 * 批量更新区域字典状态
 */
export async function batchUpdateRegionDictStatus(params: {
  ids: number[];
  status: number;
}): Promise<API.ResType<{ message: string }>> {
  return request('/admin/region-dict/batch-status', {
    method: 'PUT',
    data: params,
  });
}

/**
 * 启用/禁用区域字典
 */
export async function toggleRegionDictStatus(
  id: number,
): Promise<API.ResType<{ status: number; message: string }>> {
  return request(`/admin/region-dict/${id}/toggle-status`, {
    method: 'POST',
  });
}

/**
 * 类型字典相关接口
 */

// 类型字典数据类型
export interface TypeDict {
  id: number;
  typeCode: string;
  typeName: string;
  parentId?: number | null;
  status: number;
  sort: number;
  typeDesc?: string;
  createdAt?: string;
  updatedAt?: string;
  parent?: TypeDict | null;
  children?: TypeDict[];
}

// 类型字典创建参数
export interface CreateTypeDictParams {
  typeCode: string;
  typeName: string;
  parentId?: number | null;
  status?: number;
  sort?: number;
  typeDesc?: string;
}

// 类型字典更新参数
export interface UpdateTypeDictParams {
  typeCode?: string;
  typeName?: string;
  parentId?: number | null;
  status?: number;
  sort?: number;
  typeDesc?: string;
}

// 类型字典列表查询参数
export interface GetTypeDictListParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  status?: number;
  parentId?: number;
}

// 类型字典列表响应
export interface TypeDictListResponse {
  list: TypeDict[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 创建类型字典
 */
export async function createTypeDict(
  params: CreateTypeDictParams,
): Promise<API.ResType<TypeDict>> {
  return request('/admin/type-dict/', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新类型字典
 */
export async function updateTypeDict(
  id: number,
  params: UpdateTypeDictParams,
): Promise<API.ResType<TypeDict>> {
  return request(`/admin/type-dict/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除类型字典
 */
export async function deleteTypeDict(
  id: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/type-dict/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取类型字典详情
 */
export async function getTypeDictDetail(
  id: number,
): Promise<API.ResType<TypeDict>> {
  return request(`/admin/type-dict/${id}`, {
    method: 'GET',
  });
}

/**
 * 获取类型字典列表（分页）
 */
export async function getTypeDictList(
  params?: GetTypeDictListParams,
): Promise<API.ResType<TypeDictListResponse>> {
  return request('/admin/type-dict/', {
    method: 'GET',
    params,
  });
}

/**
 * 获取类型字典树形结构
 */
export async function getTypeDictTree(): Promise<API.ResType<TypeDict[]>> {
  return request('/admin/type-dict/tree', {
    method: 'GET',
  });
}

/**
 * 获取所有类型字典（缓存）
 */
export async function getAllTypeDict(): Promise<API.ResType<TypeDict[]>> {
  return request('/admin/type-dict/all', {
    method: 'GET',
  });
}

/**
 * 批量更新类型字典状态
 */
export async function batchUpdateTypeDictStatus(params: {
  ids: number[];
  status: number;
}): Promise<API.ResType<{ message: string }>> {
  return request('/admin/type-dict/batch-status', {
    method: 'PUT',
    data: params,
  });
}

/**
 * 启用/禁用类型字典
 */
export async function toggleTypeDictStatus(
  id: number,
): Promise<API.ResType<{ status: number; message: string }>> {
  return request(`/admin/type-dict/${id}/toggle-status`, {
    method: 'POST',
  });
}

/**
 * 关系字典相关接口
 */

// 关系字典数据类型
export interface RelationshipDict {
  id: number;
  relationCode: string;
  relationName: string;
  parentId?: number | null;
  status: number;
  sort: number;
  relationDesc?: string;
  createdAt?: string;
  updatedAt?: string;
  parent?: RelationshipDict | null;
  children?: RelationshipDict[];
}

// 关系字典创建参数
export interface CreateRelationshipDictParams {
  relationCode: string;
  relationName: string;
  parentId?: number | null;
  status?: number;
  sort?: number;
  relationDesc?: string;
}

// 关系字典更新参数
export interface UpdateRelationshipDictParams {
  relationCode?: string;
  relationName?: string;
  parentId?: number | null;
  status?: number;
  sort?: number;
  relationDesc?: string;
}

// 关系字典列表查询参数
export interface GetRelationshipDictListParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  status?: number;
  parentId?: number;
}

// 关系字典列表响应
export interface RelationshipDictListResponse {
  list: RelationshipDict[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 创建关系字典
 */
export async function createRelationshipDict(
  params: CreateRelationshipDictParams,
): Promise<API.ResType<RelationshipDict>> {
  return request('/admin/relationship-dict/', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新关系字典
 */
export async function updateRelationshipDict(
  id: number,
  params: UpdateRelationshipDictParams,
): Promise<API.ResType<RelationshipDict>> {
  return request(`/admin/relationship-dict/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除关系字典
 */
export async function deleteRelationshipDict(
  id: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/relationship-dict/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取关系字典详情
 */
export async function getRelationshipDictDetail(
  id: number,
): Promise<API.ResType<RelationshipDict>> {
  return request(`/admin/relationship-dict/${id}`, {
    method: 'GET',
  });
}

/**
 * 获取关系字典列表（分页）
 */
export async function getRelationshipDictList(
  params?: GetRelationshipDictListParams,
): Promise<API.ResType<RelationshipDictListResponse>> {
  return request('/admin/relationship-dict/', {
    method: 'GET',
    params,
  });
}

/**
 * 获取所有关系字典（缓存）
 */
export async function getAllRelationshipDict(): Promise<API.ResType<RelationshipDict[]>> {
  return request('/admin/relationship-dict/all', {
    method: 'GET',
  });
}

/**
 * 批量更新关系字典状态
 */
export async function batchUpdateRelationshipDictStatus(params: {
  ids: number[];
  status: number;
}): Promise<API.ResType<{ message: string }>> {
  return request('/admin/relationship-dict/batch-status', {
    method: 'PUT',
    data: params,
  });
}

/**
 * 启用/禁用关系字典
 */
export async function toggleRelationshipDictStatus(
  id: number,
): Promise<API.ResType<{ status: number; message: string }>> {
  return request(`/admin/relationship-dict/${id}/toggle-status`, {
    method: 'POST',
  });
}
